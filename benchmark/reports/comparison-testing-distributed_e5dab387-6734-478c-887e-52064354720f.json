{"id": "e5dab387-6734-478c-887e-52064354720f", "name": "comparison-testing-distributed", "description": "Performance comparison benchmark", "status": "completed", "config": {"name": "comparison-testing-distributed", "description": "Performance comparison benchmark", "strategy": "testing", "mode": "distributed", "max_agents": 5, "max_tasks": 100, "timeout": 3600, "task_timeout": 30, "max_retries": 2, "parallel": false, "background": false, "monitoring": true, "quality_threshold": 0.8, "resource_limits": {"max_memory_mb": 1024, "max_cpu_percent": 80, "max_network_mbps": 100}, "output_formats": ["json"], "output_directory": "./reports", "verbose": false}, "tasks": [{"id": "037de6b9-3b3f-43d2-9298-c9f0410c7156", "objective": "Create test suite for authentication module", "description": "Benchmark task: Create test suite for authentication module", "strategy": "testing", "mode": "distributed", "parameters": {}, "timeout": 30, "max_retries": 2, "priority": 1, "status": "pending", "created_at": "2025-06-17T16:57:33.251607", "started_at": null, "completed_at": null, "duration": null, "assigned_agents": [], "parent_task_id": null, "subtasks": [], "dependencies": []}], "results": [{"id": "d58dd97c-9baf-4e85-bece-507346d083e9", "task_id": "037de6b9-3b3f-43d2-9298-c9f0410c7156", "agent_id": "testing-agent", "status": "success", "output": {"test_results": "Testing completed for: Create test suite for authentication module", "tests_run": 25, "tests_passed": 24, "coverage": 0.92}, "errors": [], "warnings": [], "performance_metrics": {"execution_time": 0.120265, "queue_time": 0.0, "throughput": 0.0, "success_rate": 1.0, "error_rate": 0.0, "retry_count": 0, "coordination_overhead": 0.0, "communication_latency": 0.0}, "quality_metrics": {"accuracy_score": 0.0, "completeness_score": 0.0, "consistency_score": 0.0, "relevance_score": 0.0, "overall_quality": 0.0, "review_score": null, "automated_score": null}, "resource_usage": {"cpu_percent": 18.0, "memory_mb": 160, "network_bytes_sent": 0, "network_bytes_recv": 0, "disk_bytes_read": 0, "disk_bytes_write": 0, "peak_memory_mb": 0.0, "average_cpu_percent": 0.0}, "execution_details": {}, "created_at": "2025-06-17T16:57:33.371980", "started_at": "2025-06-17T16:57:33.251649", "completed_at": "2025-06-17T16:57:33.371940", "duration": 0.120291}], "metrics": {"total_tasks": 1, "completed_tasks": 0, "failed_tasks": 0, "total_agents": 0, "active_agents": 0, "average_execution_time": 0.120265, "total_execution_time": 0.120265, "success_rate": 0.0, "throughput": 0.0, "resource_efficiency": 0.0, "coordination_efficiency": 0.0, "quality_score": 0.0, "peak_memory_usage": 0.0, "total_cpu_time": 0.0, "network_overhead": 0.0}, "created_at": "2025-06-17T16:57:33.251630", "started_at": "2025-06-17T16:57:33.251634", "completed_at": "2025-06-17T16:57:33.372002", "duration": 0.120368, "error_log": [], "metadata": {}}