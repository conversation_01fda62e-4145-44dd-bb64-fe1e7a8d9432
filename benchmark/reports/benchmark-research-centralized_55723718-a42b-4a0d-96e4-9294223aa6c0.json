{"id": "55723718-a42b-4a0d-96e4-9294223aa6c0", "name": "benchmark-research-centralized", "description": "Benchmark: Test research task", "status": "completed", "config": {"name": "benchmark-research-centralized", "description": "Benchmark: Test research task", "strategy": "research", "mode": "centralized", "max_agents": 5, "max_tasks": 100, "timeout": 3600, "task_timeout": 300, "max_retries": 3, "parallel": false, "background": false, "monitoring": false, "quality_threshold": 0.8, "resource_limits": {"max_memory_mb": 1024, "max_cpu_percent": 80, "max_network_mbps": 100}, "output_formats": ["json"], "output_directory": "./reports", "verbose": true}, "tasks": [{"id": "a88747e3-6746-4f9b-826d-7f8924072149", "objective": "Test research task", "description": "Benchmark task: Test research task", "strategy": "research", "mode": "centralized", "parameters": {}, "timeout": 300, "max_retries": 3, "priority": 1, "status": "pending", "created_at": "2025-06-14T16:34:42.741878", "started_at": null, "completed_at": null, "duration": null, "assigned_agents": [], "parent_task_id": null, "subtasks": [], "dependencies": []}], "results": [{"id": "1b890d24-eae0-4ef3-b25f-a04fa900a709", "task_id": "a88747e3-6746-4f9b-826d-7f8924072149", "agent_id": "research-agent", "status": "success", "output": {"research_findings": "Research completed for: Test research task", "sources": ["academic papers", "documentation", "best practices"], "methodology": "comprehensive analysis"}, "errors": [], "warnings": [], "performance_metrics": {"execution_time": 0.100282, "queue_time": 0.0, "throughput": 0.0, "success_rate": 1.0, "error_rate": 0.0, "retry_count": 0, "coordination_overhead": 0.0, "communication_latency": 0.0}, "quality_metrics": {"accuracy_score": 0.0, "completeness_score": 0.0, "consistency_score": 0.0, "relevance_score": 0.0, "overall_quality": 0.0, "review_score": null, "automated_score": null}, "resource_usage": {"cpu_percent": 15.0, "memory_mb": 128, "network_bytes_sent": 0, "network_bytes_recv": 0, "disk_bytes_read": 0, "disk_bytes_write": 0, "peak_memory_mb": 0.0, "average_cpu_percent": 0.0}, "execution_details": {}, "created_at": "2025-06-14T16:34:42.842266", "started_at": "2025-06-14T16:34:42.741923", "completed_at": "2025-06-14T16:34:42.842229", "duration": 0.100306}], "metrics": {"total_tasks": 1, "completed_tasks": 1, "failed_tasks": 0, "total_agents": 0, "active_agents": 0, "average_execution_time": 0.100282, "total_execution_time": 0.100282, "success_rate": 1.0, "throughput": 0.0, "resource_efficiency": 0.0, "coordination_efficiency": 0.0, "quality_score": 0.0, "peak_memory_usage": 0.0, "total_cpu_time": 0.0, "network_overhead": 0.0}, "created_at": "2025-06-14T16:34:42.741908", "started_at": "2025-06-14T16:34:42.741914", "completed_at": "2025-06-14T16:34:42.842292", "duration": 0.100378, "error_log": [], "metadata": {}}