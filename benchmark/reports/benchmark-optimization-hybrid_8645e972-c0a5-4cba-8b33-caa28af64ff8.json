{"id": "8645e972-c0a5-4cba-8b33-caa28af64ff8", "name": "benchmark-optimization-hybrid", "description": "Benchmark: Test hybrid coordination", "status": "completed", "config": {"name": "benchmark-optimization-hybrid", "description": "Benchmark: Test hybrid coordination", "strategy": "optimization", "mode": "hybrid", "max_agents": 5, "max_tasks": 100, "timeout": 3600, "task_timeout": 300, "max_retries": 3, "parallel": false, "background": false, "monitoring": false, "quality_threshold": 0.8, "resource_limits": {"max_memory_mb": 1024, "max_cpu_percent": 80, "max_network_mbps": 100}, "output_formats": ["json"], "output_directory": "./reports", "verbose": false}, "tasks": [{"id": "747a1d94-1ea2-4561-9d16-0c36e5d669c9", "objective": "Test hybrid coordination", "description": "Benchmark task: Test hybrid coordination", "strategy": "optimization", "mode": "hybrid", "parameters": {}, "timeout": 300, "max_retries": 3, "priority": 1, "status": "pending", "created_at": "2025-06-14T16:39:22.049359", "started_at": null, "completed_at": null, "duration": null, "assigned_agents": [], "parent_task_id": null, "subtasks": [], "dependencies": []}], "results": [{"id": "5f6e4c4d-ce49-474b-9be9-f0fc6b34bff4", "task_id": "747a1d94-1ea2-4561-9d16-0c36e5d669c9", "agent_id": "optimization-agent", "status": "success", "output": {"optimization_results": "Optimization completed for: Test hybrid coordination", "performance_gain": "25% improvement", "optimizations_applied": ["caching", "indexing", "compression"]}, "errors": [], "warnings": [], "performance_metrics": {"execution_time": 0.180332, "queue_time": 0.0, "throughput": 0.0, "success_rate": 1.0, "error_rate": 0.0, "retry_count": 0, "coordination_overhead": 0.0, "communication_latency": 0.0}, "quality_metrics": {"accuracy_score": 0.0, "completeness_score": 0.0, "consistency_score": 0.0, "relevance_score": 0.0, "overall_quality": 0.0, "review_score": null, "automated_score": null}, "resource_usage": {"cpu_percent": 30.0, "memory_mb": 320, "network_bytes_sent": 0, "network_bytes_recv": 0, "disk_bytes_read": 0, "disk_bytes_write": 0, "peak_memory_mb": 0.0, "average_cpu_percent": 0.0}, "execution_details": {}, "created_at": "2025-06-14T16:39:22.229800", "started_at": "2025-06-14T16:39:22.049405", "completed_at": "2025-06-14T16:39:22.229762", "duration": 0.180357}], "metrics": {"total_tasks": 1, "completed_tasks": 1, "failed_tasks": 0, "total_agents": 0, "active_agents": 0, "average_execution_time": 0.180332, "total_execution_time": 0.180332, "success_rate": 1.0, "throughput": 0.0, "resource_efficiency": 0.0, "coordination_efficiency": 0.0, "quality_score": 0.0, "peak_memory_usage": 0.0, "total_cpu_time": 0.0, "network_overhead": 0.0}, "created_at": "2025-06-14T16:39:22.049389", "started_at": "2025-06-14T16:39:22.049395", "completed_at": "2025-06-14T16:39:22.229824", "duration": 0.180429, "error_log": [], "metadata": {}}