{"id": "02e44433-4487-4983-8cb5-dfd39be0e393", "name": "demo-research-distributed", "description": "Demo: Research Strategy - Distributed", "status": "completed", "config": {"name": "demo-research-distributed", "description": "Demo: Research Strategy - Distributed", "strategy": "research", "mode": "distributed", "max_agents": 5, "max_tasks": 100, "timeout": 3600, "task_timeout": 300, "max_retries": 3, "parallel": false, "background": false, "monitoring": true, "quality_threshold": 0.8, "resource_limits": {"max_memory_mb": 1024, "max_cpu_percent": 80, "max_network_mbps": 100}, "output_formats": ["json"], "output_directory": "./demo_reports", "verbose": true}, "tasks": [{"id": "23f632c9-6f7b-4009-a101-b9d86a2400af", "objective": "Research cloud architecture patterns and best practices", "description": "Benchmark task: Research cloud architecture patterns and best practices", "strategy": "research", "mode": "distributed", "parameters": {}, "timeout": 300, "max_retries": 3, "priority": 1, "status": "pending", "created_at": "2025-06-14T16:40:46.810881", "started_at": null, "completed_at": null, "duration": null, "assigned_agents": [], "parent_task_id": null, "subtasks": [], "dependencies": []}], "results": [{"id": "f067f9f7-b9be-4729-bcef-1708f78f624a", "task_id": "23f632c9-6f7b-4009-a101-b9d86a2400af", "agent_id": "research-agent", "status": "success", "output": {"research_findings": "Research completed for: Research cloud architecture patterns and best practices", "sources": ["academic papers", "documentation", "best practices"], "methodology": "comprehensive analysis"}, "errors": [], "warnings": [], "performance_metrics": {"execution_time": 0.100266, "queue_time": 0.0, "throughput": 0.0, "success_rate": 1.0, "error_rate": 0.0, "retry_count": 0, "coordination_overhead": 0.0, "communication_latency": 0.0}, "quality_metrics": {"accuracy_score": 0.0, "completeness_score": 0.0, "consistency_score": 0.0, "relevance_score": 0.0, "overall_quality": 0.0, "review_score": null, "automated_score": null}, "resource_usage": {"cpu_percent": 15.0, "memory_mb": 128, "network_bytes_sent": 0, "network_bytes_recv": 0, "disk_bytes_read": 0, "disk_bytes_write": 0, "peak_memory_mb": 0.0, "average_cpu_percent": 0.0}, "execution_details": {}, "created_at": "2025-06-14T16:40:46.911273", "started_at": "2025-06-14T16:40:46.810931", "completed_at": "2025-06-14T16:40:46.911226", "duration": 0.100295}], "metrics": {"total_tasks": 1, "completed_tasks": 1, "failed_tasks": 0, "total_agents": 0, "active_agents": 0, "average_execution_time": 0.100266, "total_execution_time": 0.100266, "success_rate": 1.0, "throughput": 0.0, "resource_efficiency": 0.0, "coordination_efficiency": 0.0, "quality_score": 0.0, "peak_memory_usage": 0.0, "total_cpu_time": 0.0, "network_overhead": 0.0}, "created_at": "2025-06-14T16:40:46.810910", "started_at": "2025-06-14T16:40:46.810916", "completed_at": "2025-06-14T16:40:46.911311", "duration": 0.100395, "error_log": [], "metadata": {}}