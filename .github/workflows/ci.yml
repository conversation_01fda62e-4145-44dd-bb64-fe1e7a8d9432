name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop, feature/* ]
  pull_request:
    branches: [ main ]
  schedule:
    # Run tests daily at 2 AM UTC
    - cron: '0 2 * * *'

env:
  NODE_VERSION: '20.x'

jobs:
  # Code quality and security checks
  security:
    name: Security & Code Quality
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run security audit
        run: |
          # Check for security vulnerabilities in dependencies
          npm audit --audit-level high
          
      - name: Lint code
        run: |
          npm run lint

      - name: Format check
        run: |
          npm run format || echo "Format check skipped - not configured"

      - name: Type check
        run: |
          npm run typecheck

  # Unit and integration tests
  test:
    name: Test Suite
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        os: [ubuntu-latest]
        test-type: [unit, integration, e2e]
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Cache Node modules
        uses: actions/cache@v4
        with:
          path: ~/.npm
          key: ${{ runner.os }}-node-${{ hashFiles('**/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-node-

      - name: Install dependencies
        run: npm ci

      - name: Create test directories
        run: |
          mkdir -p test-results/coverage
          mkdir -p test-results/reports

      - name: Run unit tests
        if: matrix.test-type == 'unit'
        uses: nick-invision/retry@v2
        with:
          timeout_minutes: 10
          max_attempts: 3
          command: npm test -- --testPathPattern=tests/unit --coverage --coverageDirectory=test-results/coverage

      - name: Run integration tests  
        if: matrix.test-type == 'integration'
        uses: nick-invision/retry@v2
        with:
          timeout_minutes: 15
          max_attempts: 3
          command: npm test -- --testPathPattern=tests/integration --coverage --coverageDirectory=test-results/coverage

      - name: Run end-to-end tests
        if: matrix.test-type == 'e2e'
        uses: nick-invision/retry@v2
        with:
          timeout_minutes: 20
          max_attempts: 3
          command: npm test -- --testPathPattern=tests/e2e --coverage --coverageDirectory=test-results/coverage

      - name: Generate coverage report
        if: matrix.os == 'ubuntu-latest'
        run: |
          npx jest --coverage --coverageReporters=lcov --coverageDirectory=test-results/coverage

      - name: Upload coverage to Codecov
        if: matrix.os == 'ubuntu-latest' && matrix.test-type == 'unit'
        uses: codecov/codecov-action@v4
        with:
          file: test-results/coverage/lcov.info
          flags: ${{ matrix.test-type }}
          name: codecov-${{ matrix.os }}-${{ matrix.test-type }}
          token: ${{ secrets.CODECOV_TOKEN }}

      - name: Archive test results
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: test-results-${{ matrix.os }}-${{ matrix.test-type }}
          path: test-results/
          retention-days: 30

  # Memory subdirectory testing (Vitest)
  memory-test:
    name: Memory Subdirectory Tests
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install root dependencies
        run: npm ci

      - name: Install memory dependencies
        run: |
          cd memory
          npm ci

      - name: Run memory tests
        run: |
          cd memory
          npm test

      - name: Archive memory test results
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: memory-test-results
          path: memory/coverage/
          retention-days: 30

  # Merge coverage reports
  coverage-merge:
    name: Merge Coverage Reports
    runs-on: ubuntu-latest
    needs: [test, memory-test]
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Download all coverage artifacts
        uses: actions/download-artifact@v4
        with:
          pattern: test-results-*
          path: coverage-artifacts/

      - name: Download memory coverage
        uses: actions/download-artifact@v4
        with:
          name: memory-test-results
          path: coverage-artifacts/memory/

      - name: Install coverage tools
        run: npm install -g nyc

      - name: Merge coverage reports
        run: |
          mkdir -p merged-coverage
          find coverage-artifacts -name "lcov.info" -o -name "coverage-final.json" | while read file; do
            cp "$file" "merged-coverage/$(basename $file)-$(date +%s)"
          done
          nyc merge merged-coverage coverage-final.json
          nyc report --reporter=lcov --report-dir=final-coverage

      - name: Upload merged coverage to Codecov
        uses: codecov/codecov-action@v4
        with:
          file: final-coverage/lcov.info
          flags: merged
          name: codecov-merged
          token: ${{ secrets.CODECOV_TOKEN }}

  # Performance and load testing
  performance:
    name: Performance Tests
    runs-on: ubuntu-latest
    needs: [test]
    if: github.ref == 'refs/heads/main'
    continue-on-error: true
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run performance tests
        run: |
          npm test -- --testPathPattern=tests/performance --json --outputFile=performance-results.json || echo "Performance tests not configured"

      - name: Archive performance results
        uses: actions/upload-artifact@v4
        with:
          name: performance-results
          path: performance-results.json
        if: always()

      - name: Performance regression check
        run: |
          # Compare with baseline performance metrics
          npx tsx scripts/check-performance-regression.ts || echo "Performance regression check not configured"

  # Build and package
  build:
    name: Build & Package
    runs-on: ${{ matrix.os }}
    needs: [security]
    strategy:
      fail-fast: false
      matrix:
        os: [ubuntu-latest]
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Build TypeScript
        run: |
          npm run build

      - name: Test CLI binary (Unix)
        if: runner.os != 'Windows'
        run: |
          chmod +x ./claude-flow
          ./claude-flow --help || echo "CLI test skipped"

      - name: Test CLI binary (Windows)
        if: runner.os == 'Windows'
        run: |
          node cli.js --help || echo "CLI test skipped"

      - name: Archive build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: build-${{ matrix.os }}
          path: |
            dist/
            cli.js
            claude-flow
          retention-days: 90

  # Integration testing with real Claude API
  claude-integration:
    name: Claude API Integration
    runs-on: ubuntu-latest
    needs: [build]
    if: github.ref == 'refs/heads/main'
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Download build artifacts
        uses: actions/download-artifact@v4
        with:
          name: build-Linux

      - name: Run Claude integration tests
        env:
          ANTHROPIC_API_KEY: ${{ secrets.ANTHROPIC_API_KEY }}
        run: |
          chmod +x claude-flow
          npm test -- --testPathPattern=tests/integration/claude --verbose || echo "Claude integration tests not configured"
        continue-on-error: true

  # Documentation and examples validation
  docs:
    name: Documentation & Examples
    runs-on: ubuntu-latest
    continue-on-error: true
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Validate example configurations
        run: |
          npx tsx scripts/validate-examples.ts || echo "Example validation not configured"

      - name: Test documentation code samples
        run: |
          npm test -- --testPathPattern=tests/docs || echo "Documentation tests not configured"

      - name: Check for broken links
        run: |
          npx tsx scripts/check-links.ts || echo "Link checking not configured"

  # Deployment and release
  deploy:
    name: Deploy & Release
    runs-on: ubuntu-latest
    needs: [security, test, performance, build, claude-integration, docs]
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          registry-url: 'https://registry.npmjs.org'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Download all artifacts
        uses: actions/download-artifact@v4

      - name: Prepare release
        run: |
          # Semantic versioning based on commit messages
          # Semantic release plugins should be in devDependencies
          
      - name: Semantic Release
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          NPM_TOKEN: ${{ secrets.NPM_TOKEN }}
        run: |
          semantic-release

      - name: NPM Publish
        if: steps.semantic-release.outputs.new_release_published == 'true'
        run: |
          npm publish
        env:
          NODE_AUTH_TOKEN: ${{ secrets.NPM_TOKEN }}

  # Monitoring and alerts
  monitoring:
    name: Setup Monitoring
    runs-on: ubuntu-latest
    needs: [deploy]
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    continue-on-error: true
    
    steps:
      - name: Setup monitoring
        run: |
          echo "Setting up monitoring dashboards and alerts"
          # This would integrate with monitoring services

      - name: Notify deployment
        uses: 8398a7/action-slack@v3
        with:
          status: ${{ job.status }}
          channel: '#claude-flow-deployments'
          webhook_url: ${{ secrets.SLACK_WEBHOOK }}
        if: always()

# Workflow notifications
  notify:
    name: Notify Results
    runs-on: ubuntu-latest
    needs: [security, test, performance, build, claude-integration, docs]
    if: always()
    
    steps:
      - name: Workflow Summary
        run: |
          echo "## Workflow Summary" >> $GITHUB_STEP_SUMMARY
          echo "- Security: ${{ needs.security.result }}" >> $GITHUB_STEP_SUMMARY
          echo "- Tests: ${{ needs.test.result }}" >> $GITHUB_STEP_SUMMARY
          echo "- Performance: ${{ needs.performance.result }}" >> $GITHUB_STEP_SUMMARY
          echo "- Build: ${{ needs.build.result }}" >> $GITHUB_STEP_SUMMARY
          echo "- Claude Integration: ${{ needs.claude-integration.result }}" >> $GITHUB_STEP_SUMMARY
          echo "- Documentation: ${{ needs.docs.result }}" >> $GITHUB_STEP_SUMMARY