version: '3.8'

services:
  # API Service
  api:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: rest-api-advanced
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
      - MONGODB_URI=mongodb://mongodb:27017/rest-api-advanced
      - REDIS_HOST=redis
      - REDIS_PORT=6379
    depends_on:
      - mongodb
      - redis
    volumes:
      - ./src:/app/src
      - ./uploads:/app/uploads
      - ./logs:/app/logs
      - /app/node_modules
    networks:
      - api-network
    command: npm run dev

  # MongoDB Service
  mongodb:
    image: mongo:7.0
    container_name: rest-api-mongodb
    restart: unless-stopped
    ports:
      - "27017:27017"
    environment:
      - MONGO_INITDB_ROOT_USERNAME=admin
      - MONGO_INITDB_ROOT_PASSWORD=password123
      - MONGO_INITDB_DATABASE=rest-api-advanced
    volumes:
      - mongodb-data:/data/db
      - ./scripts/mongo-init.js:/docker-entrypoint-initdb.d/mongo-init.js:ro
    networks:
      - api-network
    command: mongod --auth

  # Redis Service
  redis:
    image: redis:7-alpine
    container_name: rest-api-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    networks:
      - api-network
    command: redis-server --appendonly yes --requirepass password123

  # Redis Commander (Redis GUI)
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: rest-api-redis-commander
    restart: unless-stopped
    ports:
      - "8081:8081"
    environment:
      - REDIS_HOSTS=local:redis:6379:0:password123
    depends_on:
      - redis
    networks:
      - api-network

  # Mongo Express (MongoDB GUI)
  mongo-express:
    image: mongo-express:latest
    container_name: rest-api-mongo-express
    restart: unless-stopped
    ports:
      - "8082:8081"
    environment:
      - ME_CONFIG_MONGODB_ADMINUSERNAME=admin
      - ME_CONFIG_MONGODB_ADMINPASSWORD=password123
      - ME_CONFIG_MONGODB_SERVER=mongodb
      - ME_CONFIG_MONGODB_PORT=27017
      - ME_CONFIG_BASICAUTH_USERNAME=admin
      - ME_CONFIG_BASICAUTH_PASSWORD=admin123
    depends_on:
      - mongodb
    networks:
      - api-network

  # Mailhog (Email testing)
  mailhog:
    image: mailhog/mailhog:latest
    container_name: rest-api-mailhog
    restart: unless-stopped
    ports:
      - "1025:1025" # SMTP port
      - "8025:8025" # Web UI
    networks:
      - api-network

  # Prometheus (Monitoring)
  prometheus:
    image: prom/prometheus:latest
    container_name: rest-api-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus-data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
    networks:
      - api-network

  # Grafana (Metrics visualization)
  grafana:
    image: grafana/grafana:latest
    container_name: rest-api-grafana
    restart: unless-stopped
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_USER=admin
      - GF_SECURITY_ADMIN_PASSWORD=admin123
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana-data:/var/lib/grafana
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning:ro
    depends_on:
      - prometheus
    networks:
      - api-network

  # Nginx (Reverse proxy - optional)
  nginx:
    image: nginx:alpine
    container_name: rest-api-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
    depends_on:
      - api
    networks:
      - api-network

# Networks
networks:
  api-network:
    driver: bridge

# Volumes
volumes:
  mongodb-data:
    driver: local
  redis-data:
    driver: local
  prometheus-data:
    driver: local
  grafana-data:
    driver: local