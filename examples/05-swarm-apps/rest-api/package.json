{"name": "rest-api-example", "version": "1.0.0", "description": "REST API example built with Express.js", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "test": "jest --coverage", "test:watch": "jest --watch", "lint": "eslint src/ tests/", "format": "prettier --write \"**/*.{js,json,md}\""}, "keywords": ["rest", "api", "express", "nodejs"], "author": "<PERSON>", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "dotenv": "^16.3.1", "express-validator": "^7.0.1"}, "devDependencies": {"jest": "^29.7.0", "supertest": "^6.3.3", "nodemon": "^3.0.2", "eslint": "^8.55.0", "prettier": "^3.1.1"}, "jest": {"testEnvironment": "node", "coverageDirectory": "coverage", "collectCoverageFrom": ["src/**/*.js"]}}