{"permissions": {"allow": ["<PERSON><PERSON>(claude --dangerously-skip-permissions)", "Bash(npm install:*)", "Bash(ncu)", "Bash(ncu:*)", "Bash(npm audit:*)", "Bash(npm run typecheck:*)", "Bash(npm test)", "Bash(git checkout:*)", "Bash(git fetch:*)", "Bash(git stash push:*)", "Bash(git pull:*)", "Bash(git stash:*)", "Bash(git add:*)", "Bash(git commit:*)", "Bash(git push:*)", "<PERSON><PERSON>(claude mcp:*)", "<PERSON><PERSON>(claude:*)", "Bash(./claude-flow memory list)", "Bash(./claude-flow memory get typescript_root_causes)", "Bash(./claude-flow memory get phase1_swarm_operation_complete)", "Bash(./claude-flow memory get swarm_quick_reference)", "mcp__context7__resolve-library-id", "mcp__context7__get-library-docs", "Bash(./claude-flow:*)", "mcp__zen__debug", "Bash(grep:*)", "<PERSON><PERSON>(sed:*)", "Bash(npm run build:*)", "Bash(npx tsc:*)", "Bash(npm run lint:*)", "Bash(npm test:*)", "Bash(find:*)", "<PERSON><PERSON>(cat:*)", "mcp__code-reasoning__code-reasoning", "mcp__zen__chat", "Bash(ls:*)", "<PERSON><PERSON>(pkill:*)", "Bash(rm:*)", "Bash(kill:*)", "Bash(node:*)", "<PERSON><PERSON>(chmod:*)", "Bash(git rm:*)", "mcp__zen__thinkdeep", "Bash(awk:*)", "<PERSON><PERSON>(mkdir:*)"], "deny": []}}