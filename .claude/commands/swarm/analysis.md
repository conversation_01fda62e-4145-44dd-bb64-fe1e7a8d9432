# Analysis Swarm Command

## Usage
```bash
claude-flow swarm "Analyze data" --strategy analysis --parallel --max-agents 8
```

## Description
Data analysis and insights generation with coordinated agents.

## Strategy Features
- Data collection and preprocessing
- Statistical analysis
- Pattern recognition
- Visualization and reporting

## Best Practices
- Use parallel execution for large datasets
- Increase agent count for complex analysis
- Enable monitoring for long-running tasks
- Use appropriate output format (json, csv, html)
