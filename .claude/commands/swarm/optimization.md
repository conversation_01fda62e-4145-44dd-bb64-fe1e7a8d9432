# Optimization Swarm Command

## Usage
```bash
claude-flow swarm "Optimize performance" --strategy optimization --mode hybrid
```

## Description
Performance optimization with coordinated analysis and improvements.

## Strategy Features
- Performance profiling
- Bottleneck identification
- Optimization implementation
- Validation and testing

## Best Practices
- Use hybrid mode for adaptive optimization
- Enable monitoring for real-time metrics
- Use parallel execution for multiple optimization paths
- Set adequate timeout for thorough optimization
