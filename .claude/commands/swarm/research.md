# Research Swarm Command

## Usage
```bash
claude-flow swarm "Research objective" --strategy research --mode distributed
```

## Description
Multi-agent research coordination with distributed intelligence gathering.

## Strategy Features
- Web search and data collection
- Source credibility analysis
- Knowledge synthesis
- Report generation

## Best Practices
- Use parallel execution for multiple research threads
- Enable monitoring for real-time progress
- Set appropriate timeout for comprehensive research
- Use distributed mode for complex topics
