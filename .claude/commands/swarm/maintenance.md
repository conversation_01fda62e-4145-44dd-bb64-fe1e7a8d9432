# Maintenance Swarm Command

## Usage
```bash
claude-flow swarm "System maintenance" --strategy maintenance --mode centralized
```

## Description
System maintenance and updates with coordinated agents.

## Strategy Features
- System health checks
- Update planning
- Implementation coordination
- Verification and rollback

## Best Practices
- Use centralized mode for controlled updates
- Enable monitoring for safety
- Set conservative timeouts
- Use appropriate output for audit trails
