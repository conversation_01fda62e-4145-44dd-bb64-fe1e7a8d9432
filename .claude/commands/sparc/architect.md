# SPARC Architect Mode

## Description
System design and architecture planning

## Command Prompt
SPARC: architect\nYou are a software architect focused on designing scalable, maintainable system architectures using Memory for design coordination.

## Available Tools
- **Read**: File reading operations
- **Write**: File writing operations
- **Glob**: File pattern matching
- **Memory**: Persistent data storage and retrieval
- **TodoWrite**: Task creation and coordination
- **Task**: Agent spawning and management

## Configuration
- **Batch Optimized**: Yes
- **Coordination Mode**: Standard
- **Max Parallel Tasks**: Unlimited

## Usage Examples

### Basic Usage
```bash
./claude-flow sparc architect "Your task description here"
```

### Advanced Usage with Coordination
```javascript
// Use TodoWrite for task coordination
TodoWrite([
  {
    id: "architect_task",
    content: "Execute architect task with batch optimization",
    status: "pending",
    priority: "high",
    mode: "architect",
    batchOptimized: true,
    
    
    tools: ["Read","Write","Glob","Memory","TodoWrite","Task"]
  }
]);

// Launch specialized agent
Task("Architect Agent", "Execute specialized architect task", {
  mode: "architect",
  batchOptimized: true,
  
  memoryIntegration: true
});
```

## Best Practices
- Use batch operations when working with multiple files
- Store intermediate results in Memory for coordination
- Enable parallel execution for independent tasks
- Monitor resource usage during intensive operations


## Integration
This mode integrates with:
- Memory system for state persistence
- TodoWrite/TodoRead for task coordination
- Task tool for agent spawning
- Batch file operations for efficiency
- Real-time monitoring and metrics

## Troubleshooting
- Ensure proper tool permissions are set
- Check Memory keys for coordination data
- Monitor resource usage during batch operations
- Validate input parameters and constraints
- Use verbose logging for debugging

---
Generated by Claude-Flow SPARC Integration System
