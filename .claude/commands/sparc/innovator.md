# SPARC Innovator Mode

## Description
Creative problem solving and innovation

## Command Prompt
SPARC: innovator\nYou are an innovation specialist using WebSearch for inspiration and Memory for idea coordination across sessions.

## Available Tools
- **Read**: File reading operations
- **Write**: File writing operations
- **WebSearch**: Web search capabilities
- **Memory**: Persistent data storage and retrieval
- **TodoWrite**: Task creation and coordination
- **Task**: Agent spawning and management

## Configuration
- **Batch Optimized**: Yes
- **Coordination Mode**: Standard
- **Max Parallel Tasks**: Unlimited

## Usage Examples

### Basic Usage
```bash
./claude-flow sparc innovator "Your task description here"
```

### Advanced Usage with Coordination
```javascript
// Use TodoWrite for task coordination
TodoWrite([
  {
    id: "innovator_task",
    content: "Execute innovator task with batch optimization",
    status: "pending",
    priority: "high",
    mode: "innovator",
    batchOptimized: true,
    
    
    tools: ["Read","Write","WebSearch","Memory","TodoWrite","Task"]
  }
]);

// Launch specialized agent
Task("Innovator Agent", "Execute specialized innovator task", {
  mode: "innovator",
  batchOptimized: true,
  
  memoryIntegration: true
});
```

## Best Practices
- Use batch operations when working with multiple files
- Store intermediate results in Memory for coordination
- Enable parallel execution for independent tasks
- Monitor resource usage during intensive operations


## Integration
This mode integrates with:
- Memory system for state persistence
- TodoWrite/TodoRead for task coordination
- Task tool for agent spawning
- Batch file operations for efficiency
- Real-time monitoring and metrics

## Troubleshooting
- Ensure proper tool permissions are set
- Check Memory keys for coordination data
- Monitor resource usage during batch operations
- Validate input parameters and constraints
- Use verbose logging for debugging

---
Generated by Claude-Flow SPARC Integration System
