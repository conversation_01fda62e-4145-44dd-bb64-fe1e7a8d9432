# SPARC Designer Mode

## Description
UI/UX design and user experience

## Command Prompt
SPARC: designer\nYou are a UI/UX designer using Memory for design coordination and TodoWrite for design process management.

## Available Tools
- **Read**: File reading operations
- **Write**: File writing operations
- **Edit**: File editing and modification
- **Memory**: Persistent data storage and retrieval
- **TodoWrite**: Task creation and coordination

## Configuration
- **Batch Optimized**: Yes
- **Coordination Mode**: Standard
- **Max Parallel Tasks**: Unlimited

## Usage Examples

### Basic Usage
```bash
./claude-flow sparc designer "Your task description here"
```

### Advanced Usage with Coordination
```javascript
// Use TodoWrite for task coordination
TodoWrite([
  {
    id: "designer_task",
    content: "Execute designer task with batch optimization",
    status: "pending",
    priority: "high",
    mode: "designer",
    batchOptimized: true,
    
    
    tools: ["Read","Write","Edit","Memory","TodoWrite"]
  }
]);

// Launch specialized agent
Task("Designer Agent", "Execute specialized designer task", {
  mode: "designer",
  batchOptimized: true,
  
  memoryIntegration: true
});
```

## Best Practices
- Use batch operations when working with multiple files
- Store intermediate results in Memory for coordination
- Enable parallel execution for independent tasks
- Monitor resource usage during intensive operations


## Integration
This mode integrates with:
- Memory system for state persistence
- TodoWrite/TodoRead for task coordination
- Task tool for agent spawning
- Batch file operations for efficiency
- Real-time monitoring and metrics

## Troubleshooting
- Ensure proper tool permissions are set
- Check Memory keys for coordination data
- Monitor resource usage during batch operations
- Validate input parameters and constraints
- Use verbose logging for debugging

---
Generated by Claude-Flow SPARC Integration System
