# SPARC Workflow manager Mode

## Description
Workflow automation and process management

## Command Prompt
SPARC: workflow-manager\nYou design and manage automated workflows using TodoWrite for process planning and Task coordination for execution.

## Available Tools
- **TodoWrite**: Task creation and coordination
- **TodoRead**: Task status and progress reading
- **Task**: Agent spawning and management
- **Bash**: Command line execution
- **Memory**: Persistent data storage and retrieval

## Configuration
- **Batch Optimized**: Yes
- **Coordination Mode**: Standard
- **Max Parallel Tasks**: Unlimited

## Usage Examples

### Basic Usage
```bash
./claude-flow sparc workflow-manager "Your task description here"
```

### Advanced Usage with Coordination
```javascript
// Use TodoWrite for task coordination
TodoWrite([
  {
    id: "workflow-manager_task",
    content: "Execute workflow-manager task with batch optimization",
    status: "pending",
    priority: "high",
    mode: "workflow-manager",
    batchOptimized: true,
    
    
    tools: ["TodoWrite","TodoRead","Task","Bash","Memory"]
  }
]);

// Launch specialized agent
Task("Workflow manager Agent", "Execute specialized workflow-manager task", {
  mode: "workflow-manager",
  batchOptimized: true,
  
  memoryIntegration: true
});
```

## Best Practices
- Use batch operations when working with multiple files
- Store intermediate results in Memory for coordination
- Enable parallel execution for independent tasks
- Monitor resource usage during intensive operations


## Integration
This mode integrates with:
- Memory system for state persistence
- TodoWrite/TodoRead for task coordination
- Task tool for agent spawning
- Batch file operations for efficiency
- Real-time monitoring and metrics

## Troubleshooting
- Ensure proper tool permissions are set
- Check Memory keys for coordination data
- Monitor resource usage during batch operations
- Validate input parameters and constraints
- Use verbose logging for debugging

---
Generated by Claude-Flow SPARC Integration System
