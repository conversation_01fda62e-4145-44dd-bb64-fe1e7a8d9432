{"permissions": {"allow": ["Bash(*)", "Read(*)", "Write(*)", "Edit(*)", "MultiEdit(*)", "Glob(*)", "Grep(*)", "LS(*)", "NotebookEdit(*)", "NotebookRead(*)", "WebFetch(*)", "WebSearch(*)", "TodoRead", "TodoWrite", "Agent(*)"], "deny": []}, "env": {"BASH_DEFAULT_TIMEOUT_MS": "300000", "BASH_MAX_TIMEOUT_MS": "600000", "BASH_MAX_OUTPUT_LENGTH": "500000", "CLAUDE_BASH_MAINTAIN_PROJECT_WORKING_DIR": "true", "MAX_THINKING_TOKENS": "32000", "MCP_TIMEOUT": "60000", "MCP_TOOL_TIMEOUT": "120000", "DISABLE_COST_WARNINGS": "1", "DISABLE_NON_ESSENTIAL_MODEL_CALLS": "0", "CLAUDE_CODE_MAX_OUTPUT_TOKENS": "8192"}, "cleanupPeriodDays": 90, "includeCoAuthoredBy": true, "automation": {"enabled": true, "defaultTimeout": 300000, "maxRetries": 3, "retryBackoff": 2000, "parallelExecution": true, "batchOperations": true, "autoSaveMemory": true, "autoCommit": false}, "claudeFlow": {"version": "1.0.72", "swarmDefaults": {"maxAgents": 10, "timeout": 3600000, "parallel": true, "monitor": true, "outputFormat": "json"}, "sparcDefaults": {"timeout": 3600000, "parallel": true, "batch": true, "memoryKey": "sparc_session"}, "memoryDefaults": {"maxSize": "1GB", "autoCompress": true, "autoCleanup": true, "indexingEnabled": true, "persistenceEnabled": true}}}