#!/bin/bash
# Background swarm launcher for Claude Code
# Usage: claude-flow-swarm-bg <objective> [options]

# Get the directory where this script is located
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
CLAUDE_FLOW="$SCRIPT_DIR/claude-flow"

# Extract objective (first non-flag argument)
OBJECTIVE=""
ARGS=()
for arg in "$@"; do
    if [[ ! "$arg" =~ ^-- ]]; then
        if [ -z "$OBJECTIVE" ]; then
            OBJECTIVE="$arg"
        else
            OBJECTIVE="$OBJECTIVE $arg"
        fi
    else
        ARGS+=("$arg")
    fi
done

# Generate swarm ID
SWARM_ID="swarm_$(date +%s)_$(head -c 6 /dev/urandom | base64 | tr -d '/+=')"
SWARM_DIR="./swarm-runs/$SWARM_ID"

# Create swarm directory
mkdir -p "$SWARM_DIR"

# Create log file
LOG_FILE="$SWARM_DIR/swarm.log"

echo "🐝 Launching swarm in background mode..."
echo "📋 Objective: $OBJECTIVE"
echo "🆔 Swarm ID: $SWARM_ID"
echo "📁 Results: $SWARM_DIR"
echo "📄 Log file: $LOG_FILE"

# Save process info
cat > "$SWARM_DIR/info.json" <<EOF
{
  "swarmId": "$SWARM_ID",
  "objective": "$OBJECTIVE",
  "startTime": "$(date -u +"%Y-%m-%dT%H:%M:%SZ")",
  "logFile": "$LOG_FILE",
  "args": $(printf '%s\n' "${ARGS[@]}" | jq -R . | jq -s .)
}
EOF

# Launch swarm in background with nohup
nohup "$CLAUDE_FLOW" swarm "$OBJECTIVE" "${ARGS[@]}" > "$LOG_FILE" 2>&1 &
PID=$!

# Save PID
echo "$PID" > "$SWARM_DIR/swarm.pid"

echo ""
echo "✅ Swarm launched in background (PID: $PID)"
echo ""
echo "Monitor progress:"
echo "  tail -f $LOG_FILE"
echo ""
echo "Check if running:"
echo "  ps -p $PID"
echo ""
echo "Stop the swarm:"
echo "  kill $PID"

# Exit immediately
exit 0