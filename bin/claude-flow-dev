#!/bin/bash
# Development wrapper for Claude-Flow CLI

# Get the directory where this script is located
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# Add Deno to PATH
export PATH="/home/<USER>/.deno/bin:$PATH"

# Run the CLI with <PERSON><PERSON>, filtering out deprecation warnings
exec deno run --allow-all --no-check "$PROJECT_ROOT/src/cli/main.ts" "$@" 2>&1 | grep -v "Import assertions are deprecated"