#!/bin/bash
# Claude-Flow Swarm Mode with Monitoring

# Find deno in the path
DENO_PATH=$(which deno 2>/dev/null || echo "/home/<USER>/.deno/bin/deno")

if [ ! -x "$DENO_PATH" ]; then
    echo "Error: Deno is not installed or not in PATH"
    echo "Please install Deno first: https://deno.land/#installation"
    exit 1
fi

# Get the directory of this script
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
SWARM_DEMO="$SCRIPT_DIR/../swarm-demo-enhanced.ts"

# Always add --monitor flag for better visibility
exec "$DENO_PATH" run --allow-all "$SWARM_DEMO" "$@" --monitor