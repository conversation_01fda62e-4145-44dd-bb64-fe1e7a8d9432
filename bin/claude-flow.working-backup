#!/usr/bin/env node
// note to AI never remove this script from the repository bin directory

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');
const os = require('os');

// Check if Deno is installed
function checkDeno() {
  try {
    const denoPath = process.env.PATH.includes('.deno/bin') ? 'deno' : path.join(os.homedir(), '.deno', 'bin', 'deno');
    require('child_process').execSync(`${denoPath} --version`, { stdio: 'ignore' });
    return denoPath;
  } catch {
    try {
      require('child_process').execSync('deno --version', { stdio: 'ignore' });
      return 'deno';
    } catch {
      return null;
    }
  }
}

const denoPath = checkDeno();

if (!denoPath) {
  console.error('Error: Deno is not installed.');
  console.error('');
  console.error('Please install Deno first:');
  console.error('  curl -fsSL https://deno.land/x/install/install.sh | sh');
  console.error('');
  console.error('Or on Windows:');
  console.error('  irm https://deno.land/install.ps1 | iex');
  process.exit(1);
}

// Use the simple CLI JavaScript file which works well
const cliPath = path.join(__dirname, '..', 'src', 'cli', 'simple-cli.js');

// Run the CLI with Deno
const deno = spawn(denoPath, ['run', '--allow-all', '--no-check', cliPath, ...process.argv.slice(2)], {
  stdio: 'inherit',
  // Don't change cwd - stay in the current directory
  env: {
    ...process.env,
    PWD: process.cwd()
  }
});

deno.on('error', (err) => {
  console.error('Failed to start Claude-Flow:', err.message);
  process.exit(1);
});

deno.on('close', (code) => {
  process.exit(code || 0);
});